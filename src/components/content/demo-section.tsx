'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import type { ReactElement } from 'react';

import { ButtonDemo } from '@/components/demo/button-demo';
import { FeatureCards } from '@/components/demo/feature-cards';
import { InputDemo } from '@/components/demo/input-demo';
import { InteractionDemo } from '@/components/demo/interaction-demo';

/**
 * 组件演示区域
 * 展示 shadcn/ui 组件库和功能特性
 */
export function DemoSection(): ReactElement {
  const t = useTranslations('home.demo');

  return (
    <section id="demo-section" className="py-20 md:py-32 bg-muted/30">
      <div className="container mx-auto px-4">
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t('subtitle')}
          </p>
        </motion.div>

        {/* UI 组件演示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-semibold mb-2">
              {t('sections.ui.title')}
            </h3>
            <p className="text-muted-foreground">
              {t('sections.ui.description')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ButtonDemo />
            <InputDemo />
          </div>
        </motion.div>

        {/* 功能演示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-semibold mb-2">
              {t('sections.features.title')}
            </h3>
            <p className="text-muted-foreground">
              {t('sections.features.description')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FeatureCards />
            <InteractionDemo />
          </div>
        </motion.div>

        {/* 配置演示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-semibold mb-2">
              {t('sections.config.title')}
            </h3>
            <p className="text-muted-foreground">
              {t('sections.config.description')}
            </p>
          </div>
          
          <div className="text-center p-8 bg-background rounded-lg border">
            <p className="text-muted-foreground">
              主题和语言切换功能已集成在导航栏中，您可以在页面顶部进行测试。
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
