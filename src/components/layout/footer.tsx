'use client';

import { Gith<PERSON>, Linkedin, Twitter, Youtube } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ReactElement } from 'react';

import { Link } from '@/i18n/routing';

/**
 * 页脚组件
 */
export function Footer(): ReactElement {
  const t = useTranslations('footer');

  const productLinks = [
    { href: '/products', label: t('products.webDevelopment') },
    { href: '/products', label: t('products.mobileApps') },
    { href: '/products', label: t('products.consulting') },
    { href: '/products', label: t('products.support') },
  ];

  const supportLinks = [
    { href: '/docs', label: t('support.documentation') },
    { href: '/help', label: t('support.helpCenter') },
    { href: '/community', label: t('support.community') },
    { href: '/contact', label: t('support.contact') },
  ];

  const socialLinks = [
    { href: 'https://twitter.com', icon: Twitter, label: t('social.twitter') },
    { href: 'https://linkedin.com', icon: Linkedin, label: t('social.linkedin') },
    { href: 'https://github.com', icon: Github, label: t('social.github') },
    { href: 'https://youtube.com', icon: Youtube, label: t('social.youtube') },
  ];

  const legalLinks = [
    { href: '/privacy', label: t('legal.privacy') },
    { href: '/terms', label: t('legal.terms') },
    { href: '/cookies', label: t('legal.cookies') },
  ];

  return (
    <footer className="bg-muted/30 border-t">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="font-semibold text-lg mb-4">{t('company.name')}</h3>
            <p className="text-muted-foreground mb-4">{t('company.description')}</p>
            <p className="text-sm text-muted-foreground">{t('company.address')}</p>
          </div>

          {/* Products */}
          <div>
            <h3 className="font-semibold text-lg mb-4">{t('products.title')}</h3>
            <ul className="space-y-2">
              {productLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="font-semibold text-lg mb-4">{t('support.title')}</h3>
            <ul className="space-y-2">
              {supportLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact & Social */}
          <div>
            <h3 className="font-semibold text-lg mb-4">{t('contact.title')}</h3>
            <div className="space-y-2 mb-4">
              <p className="text-sm text-muted-foreground">{t('contact.email')}</p>
              <p className="text-sm text-muted-foreground">{t('contact.phone')}</p>
            </div>
            
            <h4 className="font-medium mb-2">{t('social.title')}</h4>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.href}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                    aria-label={social.label}
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                );
              })}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground mb-4 md:mb-0">
            {t('legal.copyright')}
          </p>
          <div className="flex space-x-6">
            {legalLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                {link.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
}
