import type { ReactElement } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export function ButtonDemo(): ReactElement {
  const t = useTranslations('home.demo.sections.ui');

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('buttonComponents')}</CardTitle>
        <CardDescription>展示不同样式和状态的按钮组件</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex flex-wrap gap-2'>
          <Button>默认按钮</Button>
          <Button variant='secondary'>次要按钮</Button>
          <Button variant='outline'>轮廓按钮</Button>
          <Button variant='ghost'>幽灵按钮</Button>
        </div>
        <div className='flex flex-wrap gap-2'>
          <Button size='sm'>小按钮</Button>
          <Button size='default'>默认大小</Button>
          <Button size='lg'>大按钮</Button>
        </div>
        <div className='flex flex-wrap gap-2'>
          <Button disabled>禁用按钮</Button>
          <Button variant='destructive'>危险按钮</Button>
        </div>
      </CardContent>
    </Card>
  );
}
