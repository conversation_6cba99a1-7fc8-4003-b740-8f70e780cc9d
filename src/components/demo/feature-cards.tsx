import type { ReactElement } from 'react';
import { useTranslations } from 'next-intl';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export function FeatureCards(): ReactElement {
  const t = useTranslations('home.features');

  return (
    <Card>
      <CardHeader>
        <CardTitle>功能特性</CardTitle>
        <CardDescription>展示项目的核心功能特性</CardDescription>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          <div className='bg-muted rounded-lg p-4'>
            <h4 className='mb-2 font-semibold'>
              {t('items.responsive.title')}
            </h4>
            <p className='text-muted-foreground text-sm'>
              {t('items.responsive.description')}
            </p>
          </div>

          <div className='bg-muted rounded-lg p-4'>
            <h4 className='mb-2 font-semibold'>
              {t('items.accessible.title')}
            </h4>
            <p className='text-muted-foreground text-sm'>
              {t('items.accessible.description')}
            </p>
          </div>

          <div className='bg-muted rounded-lg p-4'>
            <h4 className='mb-2 font-semibold'>
              {t('items.performance.title')}
            </h4>
            <p className='text-muted-foreground text-sm'>
              {t('items.performance.description')}
            </p>
          </div>

          <div className='bg-muted rounded-lg p-4'>
            <h4 className='mb-2 font-semibold'>{t('items.seo.title')}</h4>
            <p className='text-muted-foreground text-sm'>
              {t('items.seo.description')}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
