import type { ReactElement } from 'react';
import { useTranslations } from 'next-intl';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export function InputDemo(): ReactElement {
  const t = useTranslations('home.demo.sections.ui');

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('inputComponents')}</CardTitle>
        <CardDescription>展示不同类型的输入框组件</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='space-y-2'>
          <Label htmlFor='text-input'>文本输入</Label>
          <Input id='text-input' type='text' placeholder='请输入文本...' />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='email-input'>邮箱输入</Label>
          <Input
            id='email-input'
            type='email'
            placeholder='请输入邮箱地址...'
          />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='password-input'>密码输入</Label>
          <Input
            id='password-input'
            type='password'
            placeholder='请输入密码...'
          />
        </div>
        <div className='space-y-2'>
          <Label htmlFor='disabled-input'>禁用状态</Label>
          <Input
            id='disabled-input'
            type='text'
            placeholder='禁用的输入框'
            disabled
          />
        </div>
      </CardContent>
    </Card>
  );
}
