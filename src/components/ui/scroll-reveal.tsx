'use client';

import { motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import type { ReactElement, ReactNode } from 'react';

import { presetAnimations, type AnimationConfig } from '@/lib/animations';

/**
 * 滚动触发动画的 Hook
 */
export function useInView(threshold = 0.1) {
  const [inView, setInView] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold }
    );
    if (ref.current) observer.observe(ref.current);
    return () => observer.disconnect();
  }, [threshold]);

  return { ref, inView };
}

/**
 * 滚动显示组件属性
 */
export interface ScrollRevealProps {
  children: ReactNode;
  animation?: 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight' | 'fadeIn' | 'scaleIn';
  threshold?: number;
  delay?: number;
  className?: string;
}

/**
 * 滚动显示组件
 * 当元素进入视口时触发动画
 */
export function ScrollReveal({
  children,
  animation = 'fadeIn',
  threshold = 0.1,
  delay = 0,
  className = '',
}: ScrollRevealProps): ReactElement {
  const { ref, inView } = useInView(threshold);

  const animationVariants = {
    slideUp: {
      hidden: { opacity: 0, y: 50 },
      visible: { opacity: 1, y: 0 },
    },
    slideDown: {
      hidden: { opacity: 0, y: -50 },
      visible: { opacity: 1, y: 0 },
    },
    slideLeft: {
      hidden: { opacity: 0, x: 50 },
      visible: { opacity: 1, x: 0 },
    },
    slideRight: {
      hidden: { opacity: 0, x: -50 },
      visible: { opacity: 1, x: 0 },
    },
    fadeIn: {
      hidden: { opacity: 0 },
      visible: { opacity: 1 },
    },
    scaleIn: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1 },
    },
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? 'visible' : 'hidden'}
      variants={animationVariants[animation]}
      transition={{
        duration: 0.6,
        delay: delay / 1000,
        ease: 'easeOut',
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

/**
 * 动画容器组件属性
 */
export interface AnimationContainerProps {
  children: ReactNode;
  animation: AnimationConfig;
  className?: string;
  delay?: number;
}

/**
 * 动画容器组件
 * 使用预设动画配置
 */
export function AnimationContainer({
  children,
  animation,
  className = '',
  delay = 0,
}: AnimationContainerProps): ReactElement {
  return (
    <motion.div
      initial={animation.initial}
      animate={animation.animate}
      transition={{
        ...animation.transition,
        delay: delay / 1000,
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
